<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据集管理</h2>

    <!-- 主体内容区 -->
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <!-- 操作栏 -->
      <div class="flex px-10">
        <el-button type="primary" :icon="Plus" @click="onAdd">新增</el-button>
        <el-button :icon="Upload" @click="onValidateFile">校验文件</el-button>
        <!-- <el-input
          v-model="search"
          placeholder="请输入数据集说明搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <!-- 表格区域 -->
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <!-- <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip /> -->
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <!-- <el-table-column prop="description" label="数据集说明" show-overflow-tooltip /> -->
          <el-table-column prop="diseaseType" label="初始疾病类型" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="projectLeader" label="项目负责人" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column prop="state" label="状态" />
          <el-table-column fixed="right" label="操作" width="200px">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-y-2">
                <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onEdit(row)">修改</el-button>
                <el-popconfirm v-if="row.state !== '废弃'" title="确定停用？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary">停用</el-button>
                  </template>
                </el-popconfirm>
                <el-button
                  v-if="row.state !== '废弃'"
                  link
                  type="primary"
                  :loading="uploadingRows.has(row.id)"
                  @click="onImport(row)"
                >
                  上传数据集
                </el-button>
                <el-button link type="primary" @click="onHistory(row)"> 历史数据集 </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="showAdd"
    :title="formTitle"
    width="600px"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onAddClose"
  >
    <div v-if="uploadLoading" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <DatasetForm
      v-else
      ref="datasetFormRef"
      show-database
      :show-metadata="showMetadata"
      @change-show-meatadata="(e) => (showMetadata = e)"
    />
    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 上传数据集弹窗 -->
  <el-dialog
    v-model="showDatabase"
    title="上传数据集"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="handleDatabaseBeforeClose"
    @close="onDatabaseClose"
  >
    <div v-show="!startUpload">
      <el-form ref="dbFormRef" label-position="top" :model="dbForm" :rules="dbRules">
        <el-form-item label="数据集文件" prop="dbset">
          <el-upload v-model:file-list="dbForm.dbset" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="startUpload" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button :disabled="isDatasetUploading" @click="onDatabaseClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onDatabaseConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 历史数据集弹窗 -->
  <HistoryDatasetDialog v-if="showHistory" v-model="showHistory" :current-row="currentRow!" />

  <!-- 校验文件弹窗 -->
  <el-dialog
    v-model="showValidate"
    title="校验文件"
    width="600px"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onValidateClose"
  >
    <div v-if="validateLoading" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in validateMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
    </div>
    <el-form v-else ref="validateFormRef" label-position="top" :model="validateForm" :rules="validateRules">
      <el-form-item label="元数据文件" prop="metaDataFile">
        <el-upload v-model:file-list="validateForm.metaDataFile" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
          <el-button type="primary">选择元数据文件</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="数据集文件" prop="datasetFile">
        <el-upload v-model:file-list="validateForm.datasetFile" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
          <el-button type="primary">选择数据集文件</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button v-if="!validateCompleted" @click="onValidateClose">取消</el-button>
        <el-button v-if="validateCompleted" @click="onValidateClose">关闭</el-button>
        <el-button v-if="!validateCompleted" type="primary" :loading="validateLoading" @click="onValidateConfirm"
          >校验</el-button
        >
      </span>
    </template>
  </el-dialog>

  <VerifyDialog :id="verifyId" v-model="showVerify" @success="onVerifySuccess" />
</template>

<script setup lang="ts">
  // 导入所需的依赖
  import {
    findFileInforByUserId,
    deleteEntityById_36,
    findAllDb,
    newOrUpdateEntity_10,
    isFileInforProcessing,
    getFileMessages_1_02,
    medicalDataFileInforChecker,
  } from '@/api';
  import { WarningFilled, Plus, Upload } from '@element-plus/icons-vue';
  import { ElMessage, FormInstance, UploadFiles } from 'element-plus';
  import { useRouter } from 'vue-router';
  import DatasetForm from '../components/DatasetForm.vue';
  import VerifyDialog from '../components/VerifyDialog.vue';
  import dayjs from 'dayjs';
  import HistoryDatasetDialog from '../components/HistoryDatasetDialog.vue';
  import { useUsers } from '@/store/index';
  import { upload } from '@/utils/request';
  const store = useUsers();

  // 基础数据
  const router = useRouter();
  const loading = ref(false);
  const currentRow = ref<FileInfoVO | null>(null);
  const importText = ref('');

  // 日志和错误处理
  interface LogMessage {
    text: string;
    isError: boolean;
  }

  // 校验结果类型定义
  interface ValidationResult {
    level: 'PASSED' | 'ERROR' | 'WARNING';
    type: string;
    message: string;
    dataFilePath: string;
    sheetName: string | null;
    rowIndex: number;
    columnIndex: number | null;
    columnName: string | null;
    invalidValue: any;
  }
  const logMessages = ref<LogMessage[]>([]);
  const errorMessages = ref<string[]>([]);
  const hasErrors = computed(() => errorMessages.value.length > 0);

  // 表格数据
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 新增/编辑表单相关
  const datasetFormRef = ref<any>(null);
  const showMetadata = ref(true);
  const showAdd = ref(false);
  const addLoading = ref(false);

  const formTitle = computed(() => {
    if (datasetFormRef.value) {
      return datasetFormRef.value.addForm.id ? '编辑数据集' : '新增数据集';
    }
    return '';
  });

  // 上传数据集相关
  const uploadLoading = ref(false);
  const showDatabase = ref(false);
  const databaseValue = ref(0);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const startUpload = ref(false);
  const dbFormRef = ref<FormInstance>();
  const dbForm = reactive({
    dbset: [] as UploadFiles,
  });
  const dbRules = ref({
    dbset: [{ required: true, message: '不能为空' }],
  });
  const isDatasetUploading = ref(false);

  // 校验数据相关
  const showVerify = ref(false);
  const verifyId = ref(0);

  // 校验文件相关
  const showValidate = ref(false);
  const validateLoading = ref(false);
  const validateCompleted = ref(false); // 校验是否完成
  const validateFormRef = ref<FormInstance>();
  const validateForm = reactive({
    metaDataFile: [] as UploadFiles,
    datasetFile: [] as UploadFiles,
  });
  const validateRules = ref({
    metaDataFile: [{ required: true, message: '请选择元数据文件' }],
    datasetFile: [{ required: true, message: '请选择数据集文件' }],
  });
  const validateMessages = ref<LogMessage[]>([]);

  // 上传任务查询和进度相关
  const uploadTaskPolling = ref<NodeJS.Timeout | null>(null);

  // 跟踪每行的上传状态
  const uploadingRows = ref<Set<number>>(new Set());

  // 添加日志消息的方法
  const addLogMessage = (text: string) => {
    const isError = text.includes('错误提示') || text.includes('失败');
    logMessages.value.push({ text, isError });

    // 如果是错误消息，添加到错误消息列表
    if (isError) {
      errorMessages.value.push(text);
    }

    // 保持滚动到最新消息
    nextTick(() => {
      const logContainer = document.querySelector('.log-messages');
      if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    });
  };

  // 重置日志和错误信息
  const resetLogs = () => {
    logMessages.value = [];
    errorMessages.value = [];
    importText.value = '';
  };

  // 查询是否有正在进行的数据集上传任务
  const checkDatasetUploadTask = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await isFileInforProcessing(params);
      return data;
    } catch (error) {
      console.error('查询数据集上传任务失败:', error);
      return false;
    }
  };

  // 查询是否有正在进行的元数据上传任务
  const checkMetadataUploadTask = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await isFileInforProcessing(params);
      console.log('🚀 ~ checkMetadataUploadTask ~ data:', data);
      return data;
    } catch (error) {
      console.error('查询元数据上传任务失败:', error);
      return false;
    }
  };

  // 获取数据集上传进度信息
  const getDatasetUploadProgress = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await getFileMessages_1_02(params);
      return data;
    } catch (error) {
      console.error('获取数据集上传进度失败:', error);
      return null;
    }
  };

  // 获取元数据上传进度信息
  const getMetadataUploadProgress = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await getFileMessages_1_02(params);
      return data;
    } catch (error) {
      console.error('获取元数据上传进度失败:', error);
      return null;
    }
  };

  // 开始轮询数据集上传进度
  const startDatasetUploadProgressPolling = (projectCode: string) => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
    }

    let pollCount = 0;
    const maxPollCount = 720; // 最多轮询2小时 (720 * 10秒)

    // 定义轮询函数
    const pollProgress = async () => {
      pollCount++;

      // 防止无限轮询
      if (pollCount > maxPollCount) {
        stopUploadProgressPolling();
        addLogMessage('上传任务超时，请手动刷新页面查看结果');
        return;
      }

      const progressData = await getDatasetUploadProgress(projectCode);

      if (progressData && progressData.length > 0) {
        // 清空现有消息，重新显示所有消息
        // logMessages.value = [];
        // errorMessages.value = [];

        // 直接显示接口返回的所有消息
        progressData.forEach((msg) => {
          const messageText = msg.msg || '处理中...';
          const timestamp = msg.timestamp || '处理中...';

          if (msg.code === 1) {
            // 错误消息，添加到错误信息汇总
            addLogMessage(`[${timestamp}] ${messageText}`);
            if (!errorMessages.value.includes(messageText)) {
              errorMessages.value.push(messageText);
            }
          } else {
            // 正常消息、开始消息、结束消息等
            addLogMessage(`[${timestamp}] ${messageText}`);
          }
        });

        // 检查是否完成
        const hasCompleted = progressData.some((msg: any) => msg.code === 4); // code=4是上传结束
        if (hasCompleted) {
          stopUploadProgressPolling();
          addLoading.value = false;
          addLogMessage('数据集上传完成');
          ElMessage({ type: 'success', message: '数据集上传完成' });
          // 移除当前行的上传状态
          if (currentRow.value?.id) {
            uploadingRows.value.delete(currentRow.value.id);
          }
          fetchData(); // 保持当前页码
        }
      } else {
        // 没有数据，显示提示
        addLogMessage('正在处理中，暂无进度信息...');
      }
    };

    // 立即执行第一次轮询
    pollProgress();

    // 设置定时器，每10秒执行一次
    uploadTaskPolling.value = setInterval(pollProgress, 10000);
  };

  // 开始轮询元数据上传进度
  const startMetadataUploadProgressPolling = (projectCode: string) => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
    }

    let pollCount = 0;
    const maxPollCount = 720; // 最多轮询2小时 (720 * 10秒)

    // 定义轮询函数
    const pollProgress = async () => {
      pollCount++;

      // 防止无限轮询
      if (pollCount > maxPollCount) {
        stopUploadProgressPolling();
        addLogMessage('上传任务超时，请手动刷新页面查看结果');
        return;
      }

      const progressData = await getMetadataUploadProgress(projectCode);

      if (progressData && progressData.length > 0) {
        // 直接显示接口返回的所有消息
        progressData.forEach((msg) => {
          const messageText = msg.msg || '处理中...';
          const timestamp = msg.timestamp || '处理中...';

          if (msg.code === 1) {
            // 错误消息，添加到错误信息汇总
            addLogMessage(`[${timestamp}] ${messageText}`);
            if (!errorMessages.value.includes(messageText)) {
              errorMessages.value.push(messageText);
            }
          } else {
            // 正常消息、开始消息、结束消息等
            addLogMessage(`[${timestamp}] ${messageText}`);
          }
        });

        // 检查是否完成
        const hasCompleted = progressData.some((msg: any) => msg.code === 4); // code=4是上传结束
        if (hasCompleted) {
          stopUploadProgressPolling();
          addLoading.value = false;
          addLogMessage('元数据上传完成');
          ElMessage({ type: 'success', message: '元数据上传完成' });
          fetchData(); // 保持当前页码
        }
      } else {
        // 没有数据，显示提示
        addLogMessage('正在处理中，暂无进度信息...');
      }
    };

    // 立即执行第一次轮询
    pollProgress();

    // 设置定时器，每10秒执行一次
    uploadTaskPolling.value = setInterval(pollProgress, 10000);
  };

  // 停止轮询上传进度
  const stopUploadProgressPolling = () => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
      uploadTaskPolling.value = null;
    }
  };

  // 方法定义
  const fetchData = async (pageNum?: number) => {
    try {
      loading.value = true;
      // 如果没有指定页码，使用当前页码
      const currentPage = pageNum !== undefined ? pageNum : pagination.page;
      const { data } = await findFileInforByUserId(store.user.id, currentPage, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
      // 更新当前页码
      pagination.page = currentPage;
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const onAdd = () => {
    resetLogs();
    showMetadata.value = true;
    uploadLoading.value = false; // 重置上传状态，确保显示表单而不是日志
    showAdd.value = true;
  };

  const onEdit = async (row: FileInfoVO) => {
    resetLogs();
    showMetadata.value = false;
    showAdd.value = true;
    nextTick(() => {
      if (row) {
        Object.keys(datasetFormRef.value.addForm).forEach((key) => {
          if (datasetFormRef.value.addForm[key] !== null && datasetFormRef.value.addForm[key] !== undefined) {
            datasetFormRef.value.addForm[key] = row[key];
          }
        });
      }
    });
  };

  const onAddClose = () => {
    stopUploadProgressPolling();
    addLoading.value = false;
    showAdd.value = false;
    datasetFormRef.value?.formRef?.resetFields();
    uploadLoading.value = false;
    resetLogs();
  };

  const onAddConfirm = async () => {
    if (uploadLoading.value) {
      onAddClose();
      return;
    }

    if (!datasetFormRef.value.formRef) {
      ElMessage({ type: 'warning', message: '请先导入元数据' });
      return;
    }

    const valid = await datasetFormRef.value.formRef.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      resetLogs();

      if (!datasetFormRef.value.addForm.id) {
        // 新增 - 先检查是否有正在进行的元数据上传任务
        addLogMessage('正在检查元数据上传任务状态...');
        const projectCode = datasetFormRef.value.addForm.projectCode;
        if (!projectCode) {
          ElMessage({ type: 'error', message: '请先填写课题编码缩写' });
          return;
        }
        const hasRunningTask = await checkMetadataUploadTask(projectCode);
        if (hasRunningTask) {
          addLogMessage('检测到正在进行的元数据上传任务，继续显示进度...');
          // 切换到显示上传进度状态
          uploadLoading.value = true;
          addLoading.value = true; // 设置loading状态
          // 开始轮询
          startMetadataUploadProgressPolling(projectCode);
          return;
        }

        uploadLoading.value = true;

        const databaseId = datasetFormRef.value.addForm.databaseId;

        addLogMessage('开始上传元数据...');
        const formData = new FormData();
        // 将参数对象转换为JSON字符串并作为文件上传
        const paramsBlob = new Blob([JSON.stringify(datasetFormRef.value.addForm)], { type: 'application/json' });
        formData.append('fileInforDTO', paramsBlob, 'fileInforDTO.json');

        // 不等待upload完成，直接开始上传和查询进度
        upload(`/FileInfor/uploadFileId/hasmddExt?userIdCur=${store.user.id}&cbdDatabaseId=${databaseId}`, {
          method: 'post',
          data: formData,
        }).catch((error) => {
          //因为有可能是超时错误，所以这里不处理错误
          console.error('上传元数据错误:', error);
          // addLogMessage('上传元数据失败: ' + (error.message || '未知错误'));
          // 停止轮询
          // stopUploadProgressPolling();
          // addLoading.value = false;
          // uploadLoading.value = false;
        });

        // 立即开始轮询元数据上传进度
        startMetadataUploadProgressPolling(projectCode);
      } else {
        // 编辑
        const form = {
          ...datasetFormRef.value.addForm,
          createDate: dayjs(datasetFormRef.value.addForm.createDate).format('YYYY-MM-DD'),
        };
        await newOrUpdateEntity_10(form);
        onAddClose();
        addLoading.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
        fetchData();
      }
    } catch (error) {
      console.error(error);
      ElMessage({ type: 'error', message: '操作失败，请重试' });
      addLoading.value = false;
    }
  };

  const onViewDetail = (row: FileInfoVO) => {
    router.push({ name: 'DatasetField', params: { id: row.id } });
  };

  const onDel = async (row: FileInfoVO) => {
    try {
      loading.value = true;
      await deleteEntityById_36(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const onVerifySuccess = () => {
    fetchDatabase();
    databaseValue.value = 0;
    showDatabase.value = true;
  };

  const onImport = async (row: FileInfoVO) => {
    resetLogs();
    currentRow.value = row;

    // 重置上传状态
    startUpload.value = false;
    uploadLoading.value = false;
    addLoading.value = false;

    // 添加当前行到上传状态
    uploadingRows.value.add(row.id!);

    // 检查是否有正在进行的数据集上传任务
    const projectCode = row.projectCode;
    if (!projectCode) {
      ElMessage({ type: 'error', message: '当前数据集缺少课题编码信息' });
      return;
    }

    addLogMessage('正在检查数据集上传任务状态...');
    const hasRunningTask = await checkDatasetUploadTask(projectCode);
    if (hasRunningTask) {
      addLogMessage('检测到正在进行的数据集上传任务，显示进度...');
      // 切换到显示上传进度状态
      startUpload.value = true;
      addLoading.value = true; // 设置loading状态
      // 开始轮询
      startDatasetUploadProgressPolling(projectCode);
    } else {
      addLogMessage('没有检测到正在进行的上传任务');
    }

    showDatabase.value = true;
  };

  const showHistory = ref(false);
  const onHistory = (row: FileInfoVO) => {
    currentRow.value = row;
    showHistory.value = true;
  };

  // 校验文件相关方法
  const onValidateFile = () => {
    validateMessages.value = [];
    validateLoading.value = false;
    validateCompleted.value = false;
    showValidate.value = true;
  };

  const onValidateClose = () => {
    validateLoading.value = false;
    validateCompleted.value = false;
    showValidate.value = false;
    validateFormRef.value?.resetFields();
    validateMessages.value = [];
  };

  const addValidateMessage = (text: string, isError = false) => {
    validateMessages.value.push({ text, isError });
    nextTick(() => {
      const logContainer = document.querySelector('.log-messages');
      if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    });
  };

  const onValidateConfirm = async () => {
    if (validateLoading.value) {
      return;
    }

    const valid = await validateFormRef.value?.validate();
    if (!valid) return;

    try {
      validateLoading.value = true;
      validateCompleted.value = false;
      validateMessages.value = [];

      addValidateMessage('开始校验文件...');

      const formData = new FormData();
      formData.append('metaDataFile', validateForm.metaDataFile[0].raw!);
      formData.append('datasetFile', validateForm.datasetFile[0].raw!);

      const response: { data: ValidationResult[] } = await upload('/FileInfor/medicalDataFileInforChecker', {
        method: 'post',
        data: formData,
      });

      addValidateMessage('校验完成');

      // 显示校验结果
      if (response.data && Array.isArray(response.data)) {
        const results = response.data;

        // 统计校验结果
        const passedCount = results.filter((item: ValidationResult) => item.level === 'PASSED').length;
        const errorCount = results.filter((item: ValidationResult) => item.level === 'ERROR').length;
        const warningCount = results.filter((item: ValidationResult) => item.level === 'WARNING').length;

        // 显示总体结果
        if (errorCount === 0) {
          addValidateMessage(`✅ 校验通过：共检查 ${results.length} 项，通过 ${passedCount} 项`);
        } else {
          addValidateMessage(
            `❌ 校验发现问题：共检查 ${results.length} 项，通过 ${passedCount} 项，错误 ${errorCount} 项${warningCount > 0 ? `，警告 ${warningCount} 项` : ''}`,
            true
          );
        }

        addValidateMessage('');
        addValidateMessage('=== 详细校验结果 ===');

        // 按文件分组显示结果
        const fileGroups = results.reduce((groups: Record<string, ValidationResult[]>, item: ValidationResult) => {
          const fileName = item.dataFilePath || '未知文件';
          if (!groups[fileName]) {
            groups[fileName] = [];
          }
          groups[fileName].push(item);
          return groups;
        }, {});

        Object.keys(fileGroups).forEach((fileName) => {
          addValidateMessage(`\n📄 文件: ${fileName}`);

          const fileResults = fileGroups[fileName];
          const fileErrors = fileResults.filter((item: ValidationResult) => item.level === 'ERROR');
          const fileWarnings = fileResults.filter((item: ValidationResult) => item.level === 'WARNING');
          const filePassed = fileResults.filter((item: ValidationResult) => item.level === 'PASSED');

          if (fileErrors.length > 0) {
            addValidateMessage(`   ❌ 错误 (${fileErrors.length} 项):`, true);
            fileErrors.forEach((item: ValidationResult) => {
              const location = item.sheetName ? `[${item.sheetName}]` : '';
              addValidateMessage(`      • ${location} ${item.message}`, true);
            });
          }

          if (fileWarnings.length > 0) {
            addValidateMessage(`   ⚠️ 警告 (${fileWarnings.length} 项):`);
            fileWarnings.forEach((item: ValidationResult) => {
              const location = item.sheetName ? `[${item.sheetName}]` : '';
              addValidateMessage(`      • ${location} ${item.message}`);
            });
          }

          if (filePassed.length > 0) {
            addValidateMessage(`   ✅ 通过 (${filePassed.length} 项)`);
            // 只显示前5个通过项，避免信息过多
            filePassed.slice(0, 5).forEach((item: ValidationResult) => {
              const location = item.sheetName ? `[${item.sheetName}]` : '';
              addValidateMessage(`      • ${location} ${item.message}`);
            });
            if (filePassed.length > 5) {
              addValidateMessage(`      ... 还有 ${filePassed.length - 5} 项检查通过`);
            }
          }
        });

        // 最终结论
        addValidateMessage('');
        if (errorCount === 0) {
          addValidateMessage('🎉 恭喜！所有校验项目都通过了，文件格式正确！');
        } else {
          addValidateMessage('⚠️ 请修复上述错误后重新上传文件', true);
        }

        // 设置校验完成状态
        validateCompleted.value = true;

        // 根据校验结果显示消息
        ElMessage({
          type: errorCount === 0 ? 'success' : 'warning',
          message: errorCount === 0 ? '校验通过' : '校验完成，请查看详细信息',
        });
      } else {
        addValidateMessage('校验完成，但未返回详细结果');
        validateCompleted.value = true;
        ElMessage({
          type: 'info',
          message: '校验完成',
        });
      }
    } catch (error: any) {
      console.error('校验文件失败:', error);
      addValidateMessage('校验失败: ' + (error.message || '未知错误'), true);
      validateCompleted.value = true;
      ElMessage({ type: 'error', message: '校验失败，请重试' });
    } finally {
      validateLoading.value = false;
    }
  };

  const handleDatabaseBeforeClose = (done: () => void) => {
    //上传链接建立中，不允许关闭弹窗
    if (isDatasetUploading.value) {
      ElMessage({ type: 'warning', message: '上传链接建立中，请稍候...' });
      return;
    }
    done();
  };

  const onDatabaseClose = () => {
    //上传链接建立中，不允许关闭弹窗
    if (isDatasetUploading.value) return;

    stopUploadProgressPolling();
    startUpload.value = false;
    uploadLoading.value = false; // 重置上传状态
    dbFormRef.value?.resetFields();
    showDatabase.value = false;
    addLoading.value = false;
    // 移除当前行的上传状态
    if (currentRow.value?.id) {
      uploadingRows.value.delete(currentRow.value.id);
    }
    resetLogs();
  };

  const fetchDatabase = async () => {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.error(error);
    }
  };

  const onDatabaseConfirm = async () => {
    // 如果正在显示上传进度或已完成，点击确定按钮关闭弹窗
    if (uploadLoading.value || startUpload.value) {
      onDatabaseClose();
      return;
    }

    const valid = await dbFormRef.value?.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      startUpload.value = true;
      resetLogs();

      const projectCode = currentRow.value?.projectCode;
      if (!projectCode) {
        ElMessage({ type: 'error', message: '当前数据集缺少课题编码信息' });
        return;
      }

      if (!currentRow.value?.id) {
        throw new Error('未选择数据集');
      }

      addLogMessage('开始上传数据集...');

      // 切换到显示上传进度状态
      startUpload.value = true;
      addLoading.value = true; // 设置loading状态
      isDatasetUploading.value = true;

      const formData = new FormData();
      formData.append('datasetFile', dbForm.dbset[0].raw!);
      //上传数据集等待完成才开始查询进度
      await upload(
        `/FileInfor/exportMedicalDataSetToDatabaseExt?userIdCur=${store.user.id}&fileId=${currentRow.value.id}&projectCode=${projectCode}`,
        {
          method: 'post',
          data: formData,
        }
      );

      // 立即开始轮询数据集上传进度
      startDatasetUploadProgressPolling(projectCode);
    } catch (error: any) {
      console.error('上传数据集失败:', error);
      addLoading.value = false;
      startUpload.value = false;
      // 移除当前行的上传状态
      if (currentRow.value?.id) {
        uploadingRows.value.delete(currentRow.value.id);
      }
      ElMessage({ type: 'error', message: '上传失败，请重试' });
    } finally {
      isDatasetUploading.value = false;
    }
  };

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });

  onUnmounted(() => {
    stopUploadProgressPolling();
    startUpload.value = false;
  });
</script>

<style lang="scss" scoped>
  .vertical-radio-group {
    display: block;
    .el-radio {
      display: block;
    }
  }

  .log-container {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
  }

  .log-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-family: monospace;
    background-color: #f8f9fa;
    line-height: 1.5;
    font-size: 14px;
  }

  .error-message {
    color: #f56c6c;
    font-weight: 500;
  }

  .error-summary {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff0f0;
    border-top: 1px solid #fde2e2;
  }

  .error-title {
    display: flex;
    align-items: center;
    color: #f56c6c;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;

    .el-icon {
      margin-right: 6px;
    }
  }

  .error-list {
    max-height: 100px;
    overflow-y: auto;

    p {
      margin: 4px 0;
      padding-left: 16px;
      font-size: 13px;
      color: #f56c6c;
    }
  }

  .upload-progress-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .progress-content {
    .queue-progress {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 15px;

      .queue-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .messages-list {
        .message-item {
          margin-bottom: 10px;
          padding: 8px;
          background-color: #f8f9fa;
          border-radius: 4px;

          .message-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .message-content {
            font-size: 14px;
            color: #303133;
            margin-bottom: 8px;
          }

          .message-progress {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 14px;
    color: #606266;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
</style>
